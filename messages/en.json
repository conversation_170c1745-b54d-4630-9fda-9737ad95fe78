{"site": {"title": "c30 life", "description": "This is c30's homepage."}, "navigation": {"home": "Home", "blog": "Blog", "info": "Info", "links": "Links", "mastodon": "Mastodon", "midis": "MIDI", "misskey": "<PERSON><PERSON>", "pubkeys": "Public Keys", "servers": "Servers", "environments": "Environments", "watchedAnimes": "Watched Animes", "homeTooltip": "You can also go to Home from \"c30 life\" above!"}, "home": {"welcome": "Welcome to c30 life, this is c30(ced)'s homepage.", "selfIntroduction": "Self Introduction", "introduction": "I'm a strange pot and empty existence with various hobbies.", "misskeyArt": "I manage", "misskeyArtLink": "Misskey.art", "misskeyArtManagement": ", a Misskey-based server.", "disabilities": "I have intellectual disabilities, depression, and autism spectrum disorder.", "profile": "Profile", "name": "Name", "nameValue": "c30", "nameSubValue": "ced", "age": "Age", "birthday": "4/25", "gender": "Gender", "genderValue": "Male", "genderSubValue": "hmm...", "residence": "Residence", "residenceValue": "Kanagawa", "residenceSubValue": "Yokohama", "hobbies": "Hobbies", "hobbyIllustration": "Illustration", "hobbyMusic": "Music Production & Ear Copy", "hobbyProgramming": "Programming", "hobbyFediverse": "Fediverse, Discord", "programmingLanguages": "Programming Languages", "log": "Log", "nothing": "Nothing special here"}, "language": {"japanese": "日本語", "english": "English", "switchLanguage": "Switch Language"}, "notFound": {"errorPageLoadFailed": "Error: Page load failed", "description": "Description:", "pageCouldNotBeLoaded": "The requested page could not be loaded.", "details": "Details:", "errorCode": "Error Code: 404", "errorType": "Error Type: Page Not Found", "errorMessage": "Error Message: The requested page could not be found.", "secondsToHome": "5 Seconds to Home"}, "footer": {"copyright": "Copyright © 2023 - All right reserved by c30"}, "environments": {"title": "Environment", "pc": "PC", "cpu": "CPU", "gpu": "GPU", "ram": "RAM", "storage": "Storage", "os": "OS", "earphone": "Earphone", "mouse": "Mouse", "tablet": "Tablet", "softwares": "Softwares", "software": "Software", "games": "Games", "game": "Game", "phones": "Phones", "name": "Name", "rooted": "Rooted", "yes": "Yes", "no": "No"}, "ui": {"openDrawerMenu": "Click here to open the drawer menu", "changeTheme": "Click here to change the theme", "debugBuild": "Debug Build"}}
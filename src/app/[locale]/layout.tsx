import NavBar from "@/components/navbar"
import "@/styles/globals.css"
import Footer from "@/components/footer"
import type { Metadata, Viewport } from "next"
import Drawer from "@/components/drawer"
import Fireworks from "@/components/fireworks"
import { ThemeProvider } from "next-themes"
import { NextIntlClientProvider } from "next-intl"
import { getMessages, getTranslations } from "next-intl/server"
import { notFound } from "next/navigation"
import { routing } from "@/i18n/routing"

type Props = {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}

export default async function LocaleLayout({ children, params }: Props) {
  const { locale } = await params

  // Ensure that the incoming `locale` is valid
  if (!routing.locales.includes(locale as any)) {
    notFound()
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages()
  const t = await getTranslations("ui")

  return (
    <html lang={locale} suppressHydrationWarning>
      <body className="h-screen w-screen flex flex-col">
        <NextIntlClientProvider messages={messages}>
          <ThemeProvider storageKey="theme" themes={["forest", "cupcake"]}>
            <Drawer>
              {process.env.npm_lifecycle_event === "dev" && (
                <div className="fixed bottom-0 right-0 z-50 p-2 bg-black">
                  <p className="text-xl text-red-600 animate-bounce">
                    {t("debugBuild")}
                  </p>
                </div>
              )}
              <NavBar />
              <main className="flex-grow overflow-y-auto">{children}</main>
              <Footer />
            </Drawer>
            <Fireworks />
            <a
              rel="me"
              href="https://fedibird.com/@c30"
              style={{ display: "none" }}
            >
              Fedibird
            </a>
            <a
              rel="me"
              href="https://misskey.art/@c30"
              style={{ display: "none" }}
            >
              Misskey.art
            </a>
            <a
              rel="me"
              href="https://mk.c30.life/@c30"
              style={{ display: "none" }}
            >
              至り来たり宿
            </a>
          </ThemeProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}

export function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  return params.then(({ locale }) => {
    const siteName = "c30 life"
    const description =
      locale === "ja" ? "c30のホームページです。" : "This is c30's homepage."
    const url = "https://c30.life"

    return {
      metadataBase: new URL(url),
      title: {
        default: siteName,
        template: `%s - ${siteName}`,
      },
      description,
      openGraph: {
        images: "/c30_rounded.png",
        url,
        type: "website",
        siteName,
        title: {
          default: siteName,
          template: `%s - ${siteName}`,
        },
      },
      twitter: {
        card: "summary",
        title: siteName,
        description,
        site: "@c30_eo",
        creator: "@c30_eo",
        images: "/c30_rounded.png",
      },
    }
  })
}

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
}

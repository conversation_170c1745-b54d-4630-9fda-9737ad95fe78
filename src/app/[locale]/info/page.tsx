import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import os from "node:os"
import packages from "../../../../package.json"
import licenses from "@/licenses.json"
import { getTranslations } from "next-intl/server"

type Package = {
  name: string
  version: string
}

type Dependencies = {
  [key: string]: string
}

type License = {
  name: string
  version: string
  author: string | null
  repository: string
  source: string
  license: string
  licenseText: string
}

const getData = async () => {
  return {
    host: "c30.life",
    owner: "c30",
    hostname: os.hostname(),
    runningAs: `${os.userInfo().username}@${os.hostname()}`,
    thisVersion: packages.version,
    nodeVersion: process.version,
    pnpmVersion: packages.packageManager,
  }
}

export default async function Home() {
  const data = await getData()
  const t = await getTranslations("info")

  const deps: Dependencies = packages.dependencies
  const devDeps: Dependencies = packages.devDependencies

  const packageList: Package[] = []
  const devPackageList: Package[] = []

  for (const [name, version] of Object.entries(deps)) {
    packageList.push({ name, version })
  }

  for (const [name, version] of Object.entries(devDeps)) {
    devPackageList.push({ name, version })
  }

  const licensesList: License[] = licenses as License[]

  return (
    <>
      <div className="card w-full bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">{t("title")}</h2>
          <div className="bg-zinc-400 w-full h-0.5 rounded" />
          <ul className="px-5">
            <li>
              {t("host")}: {data.host}
            </li>
            <li>
              {t("owner")}: {data.owner}
            </li>
            <li>
              {t("runningUser")}: {data.runningAs}
            </li>
            <li className="bg-zinc-500 w-full h-0.5 rounded my-1" />
            <li>
              {t("siteVersion")}: {data.thisVersion}
            </li>
            <li>
              {t("nodeVersion")}: {data.nodeVersion}
            </li>
            <li>
              {t("pnpmVersion")}: {data.pnpmVersion.replace("pnpm@", "")}
            </li>
            <li className="bg-zinc-500 w-full h-0.5 rounded my-1" />
            <li>
              {t("sitemap")}:{" "}
              <Link
                href="/sitemap.xml"
                className="link link-primary"
                target="_blank"
              >
                sitemap.xml
              </Link>
            </li>
            <li>
              {t("robots")}:{" "}
              <Link
                href="/robots.txt"
                className="link link-primary"
                target="_blank"
              >
                robots.txt
              </Link>
            </li>
            <li>
              {t("repository")}:{" "}
              <Link
                href="https://github.com/Zel9278/c30.life"
                className="link link-primary"
                target="_blank"
              >
                git:zel9278/c30.life
              </Link>
            </li>
          </ul>

          <div className="bg-zinc-400 w-full h-0.5 rounded my-2" />

          <details className="collapse collapse-arrow bg-base-200">
            <summary className="collapse-title text-xl font-medium">
              {t("dependencies")}
            </summary>
            <div className="collapse-content max-h-full">
              <ul>
                {packageList.map((pkg) => (
                  <li key={pkg.name}>
                    {pkg.name}: {pkg.version}
                  </li>
                ))}
              </ul>
            </div>
          </details>
          <details className="collapse collapse-arrow bg-base-200">
            <summary className="collapse-title text-xl font-medium">
              {t("devDependencies")}
            </summary>
            <div className="collapse-content max-h-full">
              <ul>
                {devPackageList.map((pkg) => (
                  <li key={pkg.name}>
                    {pkg.name}: {pkg.version}
                  </li>
                ))}
              </ul>
            </div>
          </details>
          <div className="bg-zinc-400 w-full h-0.5 rounded my-2" />

          <details className="collapse collapse-arrow bg-base-200">
            <summary className="collapse-title text-xl font-medium">
              {t("licenses")}
            </summary>
            <div className="collapse-content max-h-full">
              <ul>
                {licensesList.map((license) => (
                  <li key={license.name}>
                    <details className="collapse collapse-arrow bg-base-200">
                      <summary className="collapse-title text-lg font-medium">
                        {license.name}
                      </summary>
                      <div className="collapse-content">
                        <ul>
                          <li>
                            {t("version")}: {license.version}
                          </li>
                          {license.author && (
                            <li>
                              {t("author")}: {license.author}
                            </li>
                          )}
                          <li>
                            {t("repository")}:{" "}
                            <Link
                              href={license.repository}
                              className="link link-primary"
                              target="_blank"
                            >
                              {license.repository}
                            </Link>
                          </li>
                          <li>
                            {t("source")}:{" "}
                            <Link
                              href={license.source}
                              className="link link-primary"
                              target="_blank"
                            >
                              {license.source}
                            </Link>
                          </li>
                          <li>
                            {t("license")}: {license.license}
                          </li>
                          <li>
                            <details className="collapse collapse-arrow bg-base-200">
                              <summary className="collapse-title text-lg font-medium">
                                {t("licenseText")}
                              </summary>
                              <div className="collapse-content">
                                <pre>{license.licenseText}</pre>
                              </div>
                            </details>
                          </li>
                        </ul>
                      </div>
                    </details>
                  </li>
                ))}
              </ul>
            </div>
          </details>
        </div>
      </div>
    </>
  )
}

export function generateMetadata(): Metadata {
  return {
    description: "c30.lifeの情報です。",
    openGraph: {
      title: "info",
      description: "c30.lifeの情報です。",
      url: "https://c30.life/info",
    },
  }
}

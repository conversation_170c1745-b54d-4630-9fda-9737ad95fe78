"use client"

import { useEffect, useState } from "react"
import { usePathname } from "next/navigation"
import { useTranslations } from "next-intl"

export default function NotFound() {
  const pathname = usePathname()
  const [text, setText] = useState("")
  const t = useTranslations("notFound")

  useEffect(() => {
    const texts = [
      t("errorPageLoadFailed"),
      "",
      t("description"),
      t("pageCouldNotBeLoaded"),
      "",
      t("details"),
      t("errorCode"),
      t("errorType"),
      t("errorMessage"),
      "",
      t("secondsToHome"),
    ]

    async function run() {
      for (const line of texts) {
        setText((text) => `${text + line}\n`)
        await sleep(Math.random() * (200 - 10) + 10)
      }

      await sleep(5000)
      window.location.href = "/"
    }

    run()
  }, [])

  return (
    <div>
      <pre>{`User> curl c30.life${pathname}`}</pre>
      <pre>{text}</pre>
    </div>
  )
}

function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}
